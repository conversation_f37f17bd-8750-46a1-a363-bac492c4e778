import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import MainView from '@/views/MainView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'home',
      component: HomeView,
    },
    {
      path: '/chatshow',
      name: 'chatshow',
      component: MainView,
      meta: {
        title: '智能体对话运维页',
        requiresAuth: true
    }
    },
  ],
})

// 添加全局前置守卫来设置标题
router.beforeEach((to, from, next) => {
  // 从路由元信息中获取标题
  const title = to.meta?.title
  
  // 如果存在标题，则设置文档标题
  if (title) {
    document.title = title
  }
  
  next()
})

export default router
