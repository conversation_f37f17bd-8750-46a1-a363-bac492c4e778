import request from '@/utils/request'

// 获取初始数据
export function getInitialData() {
  return request({
    url: '/api/conversation/initial',
    method: 'GET'
  })
}

// 创建新对话
export function createConversation(data) {
  return request({
    url: '/api/conversation',
    method: 'POST',
    data
  })
}

// 创建新工单
export function createTicket(data) {
  return request({
    url: '/api/ticket',
    method: 'POST',
    data
  })
}

// 更新工单名称
export function updateTicketName(ticketId, newName) {
  return request({
    url: `/api/ticket/${ticketId}`,
    method: 'POST',
    data: { name: newName }
  })
}

// 删除工单
export function deleteTicket(ticketId) {
  return request({
    url: `/api/ticket/${ticketId}`,
    method: 'DELETE'
  })
}

// 获取工单下的对话
export function getTicketConversations(ticketId) {
  return request({
    url: `/api/ticket/${ticketId}/conversations`,
    method: 'GET'
  })
}