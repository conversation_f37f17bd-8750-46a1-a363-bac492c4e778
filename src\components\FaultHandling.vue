<template>
  <div>
    <!-- 标题在盒子外部居中显示 -->
    <h1 class="main-title">系统概览</h1>
    
    <!-- 当前遭遇故障卡 -->
    <div class="system-box" v-if="showFaultCard">
      <!-- 状态提示栏 -->
      <div class="status-bar" :style="severityStyle">
        <i class="iconfont icon-zhuyi-yin status-icon"></i>
        <span class="status-text">当前遭遇故障卡</span>
        <span class="severity">（{{ currentFault.severity }}）</span>
      </div>
      
      <!-- 影响应用 -->
      <div class="section-title">影响应用</div>
      <div class="app-tags">
        <span class="app-tag" v-for="app in currentFault.affectedApps" :key="app">{{ app }}</span>
      </div>
      
      <!-- 故障概要 -->
      <div class="section-title">
        <i class="iconfont icon-zhuyi-yin detail-icon"></i>
        故障概要</div>
      <div class="detail-item">
        
        {{ currentFault.summary }}
      </div>
      
      <!-- 故障状态&时间 -->
      <div class="status-info">
        <span class="status-tag" :style="{ color: statusColor }">{{ currentFault.status }}</span>
        <span class="timestamp">监测于{{ currentFault.detectedTime }}前</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// 模拟从后端获取的数据
const currentFault = ref({
  severity: 'P2 - 严重',
  affectedApps: ['应用1', '应用2'],
  summary: '数据库连接池耗尽导致部分请求失败',
  status: '解决中',
  detectedTime: '23分钟'
});

// 是否显示故障卡
const showFaultCard = ref(true);

// 根据严重等级计算样式
const severityStyle = computed(() => {
  const severity = currentFault.value.severity;
  if (severity.includes('P0')) {
    return {
      backgroundColor: '#ffcccc',
      borderColor: '#ff3333'
    };
  } else if (severity.includes('P1')) {
    return {
      backgroundColor: '#ffd9cc',
      borderColor: '#ff5722'
    };
  } else if (severity.includes('P2')) {
    return {
      backgroundColor: '#ffebcc',
      borderColor: '#ffa726'
    };
  } else {
    return {
      backgroundColor: '#f5f5f5',
      borderColor: '#e0e0e0'
    };
  }
});

// 状态标签颜色
const statusColor = computed(() => {
  const status = currentFault.value.status;
  if (status === '分析中') return '#d48806';
  if (status === '待解决') return '#f5222d';
  if (status === '解决中') return '#1890ff';
  if (status === '监测中') return '#52c41a';
  return '#8c8c8c';
});
</script>

<style scoped>
/* 重置默认样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 标题样式 */
.main-title {
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #1a1a1a;
}

/* 系统状态盒子 */
.system-box {
  max-width: 600px;
  margin: 0 auto;
  border: 1.5px solid #ffccb3;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* 状态提示栏 */
.status-bar {
  background-color: #ffd9cc;
  border-left: 4px solid #ff5722;
  border-radius: 4px;
  padding: 8px 12px;
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  width: 100%;
}

/* 状态提示图标 */
.status-icon {
  color: #ff5722;
  font-size: 20px;
  margin-right: 8px;
}

/* 状态提示文字 */
.status-text {
  font-size: 14px;
  font-weight: bold;
  color: #1a1a1a;
}

/* 严重程度标签 */
.severity {
  font-size: 14px;
  color: #d48806;
  margin-left: 4px;
  font-weight: bold;
}

/* 部分标题 */
.section-title {
  font-size: 14px;
  color: #595959;
  margin: 12px 0 8px 0;
  font-weight: bold;
}

/* 应用标签区域 */
.app-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.app-tag {
  background-color: #ffb69e;
  padding: 4px 10px;
  border-radius: 4px;
  color: #fff;
  font-size: 12px;
  cursor: default;
}

/* 详细信息项 */
.detail-item {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #333;
  margin-bottom: 12px;
  margin-left: 26px;
}

/* 详细信息图标 */
.detail-icon {
  color: #ff5722;
  font-size: 16px;
  margin-right: 8px;
}

/* 状态信息行 */
.status-info {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #8c8c8c;
  gap: 10px;
  margin-top: 4px;
  margin-left: 26px;
}

/* 状态标签 */
.status-tag {
  font-weight: 500;
}

/* 响应式适配 */
@media (max-width: 480px) {
  .system-box {
    max-width: 100%;
    padding: 10px;
  }
  .main-title {
    font-size: 20px;
  }
}
</style>