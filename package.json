{"name": "-chat-show", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint:oxlint": "oxlint . --fix -D correctness --ignore-path .gitignore", "lint:eslint": "eslint . --fix", "lint": "run-s lint:*"}, "dependencies": {"axios": "^1.11.0", "pinia": "^3.0.3", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.29.0", "@vitejs/plugin-vue": "^6.0.0", "eslint": "^9.29.0", "eslint-plugin-oxlint": "~1.1.0", "eslint-plugin-vue": "~10.2.0", "globals": "^16.2.0", "npm-run-all2": "^8.0.4", "oxlint": "~1.1.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7"}}