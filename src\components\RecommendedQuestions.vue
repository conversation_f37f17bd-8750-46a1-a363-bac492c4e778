<template>
  <div class="recommended-questions-container">
    <h3 class="section-title">推荐问题</h3>
    <div class="questions-wrapper">
      <div 
        v-for="(question, index) in questions" 
        :key="question.id"
        class="question-item"
        :style="getQuestionStyle(index)"
        @click="handleQuestionClick(question.text)"
      >
        {{ question.text }}
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const emit = defineEmits(['question-click'])

// 问题数据
const questions = ref([])

// 动画状态
const animationState = ref({
  loaded: false,
  staggerDelay: 100 // 每个问题之间的延迟(ms)
})

// 从后端获取问题数据
const fetchQuestions = async () => {
  try {
    // 模拟API请求
    const response = await new Promise(resolve => {
      setTimeout(() => {
        resolve({
          data: [
            { id: 1, text: '当前系统健康状况如何?' },
            { id: 2, text: '有哪些正在发生的故障?' },
            { id: 3, text: 'CPU使用率为什么这么高?' },
            { id: 4, text: '如何优化内存使用?' }
          ]
        })
      }, 300)
    })
    
    questions.value = response.data
    animationState.value.loaded = true
  } catch (error) {
    console.error('获取推荐问题失败:', error)
  }
}

// 处理问题点击
const handleQuestionClick = (question) => {
  emit('question-click', question)
}

// 获取问题动画样式
const getQuestionStyle = (index) => {
  if (!animationState.value.loaded) {
    return {
      transform: 'translateX(100%)',
      opacity: 0
    }
  }
  
  // 非线性缓动函数 - easeOutBack
  const easeOutBack = (x) => {
    const c1 = 1.70158;
    const c3 = c1 + 1;
    return 1 + c3 * Math.pow(x - 1, 3) + c1 * Math.pow(x - 1, 2);
  }
  
  // 计算每个问题的延迟
  const delay = index * animationState.value.staggerDelay
  const progress = Math.min(1, (Date.now() - animationState.value.loadedTime) / 500 - delay / 500)
  
  if (progress < 0) {
    return {
      transform: 'translateX(100%)',
      opacity: 0
    }
  }
  
  const easedProgress = easeOutBack(Math.min(1, progress * 1.5))
  
  return {
    transform: `translateX(${(1 - easedProgress) * 100}%)`,
    opacity: easedProgress,
    transition: 'none'
  }
}

// 组件挂载时获取数据
onMounted(() => {
  fetchQuestions().then(() => {
    animationState.value.loadedTime = Date.now()
    
    // 强制重绘以触发动画
    requestAnimationFrame(() => {
      questions.value.forEach((_, index) => {
        setTimeout(() => {
          const element = document.querySelector(`.question-item:nth-child(${index + 1})`)
          if (element) {
            element.style.transition = 'transform 0.6s cubic-bezier(0.68, -0.6, 0.32, 1.6), opacity 0.4s ease-out'
            element.style.transform = 'translateX(0)'
            element.style.opacity = 1
          }
        }, index * animationState.value.staggerDelay)
      })
    })
  })
})
</script>

<style scoped>
.recommended-questions-container {
  padding: 0 20px 15px;
  border-bottom: 1px solid #e0e0e0;
  overflow: hidden;
}

.section-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.questions-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.question-item {
  padding: 6px 12px;
  background-color: #f0f0f0;
  border-radius: 15px;
  font-size: 12px;
  color: #333;
  cursor: pointer;
  will-change: transform, opacity;
  transform: translateX(100%);
  opacity: 0;
}

.question-item:hover {
  background-color: #e0e0e0;
  transform: translateY(-2px) translateX(0) !important;
}
</style>