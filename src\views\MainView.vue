<template>
  <div class="app-container">
    <div class="main-layout">
      <ConversationSidebar 
        :conversations="conversations" 
        :activeConversationId="activeConversationId"
        @new-conversation-click="handleNewConversation"
        @conversation-selected="selectConversation"
        @create-conversation="createNewConversation"
        @update:conversations="handleUpdateConversations"
        ref="sidebar"
      />
      <div class="content-area">
        <SystemOverview v-if="showSystemOverview" />
        <ConversationMain 
          :key="activeConversationId"
          :is-new-conversation="isNewConversation"
          :showSystemOverview="showSystemOverview"
          :conversations="conversations"
          :messages="currentMessages"
          @create-conversation="createConversation"
          @send-message="handleSendMessage"
          @update-conversation="updateConversation"
          @hide-overview="hideSystemOverview"
          ref="conversation"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import ConversationSidebar from '../components/ConversationSidebar.vue';
import SystemOverview from '../components/SystemOverview.vue';
import ConversationMain from '../components/ConversationMain.vue';

const conversations = ref([
  { id: 1, name: '演示对话1', messages: [], hasShownWelcome: false },
  { id: 2, name: '演示对话2', messages: [], hasShownWelcome: false }
]);

const isNewConversation = ref(false);
const conversationMain = ref(null);
const activeConversationId = ref(null);
const showSystemOverview = ref(true);
const currentMessages = ref([]);

const emit = defineEmits([
  'create-conversation',
  'update:conversations',
  'redirect-to-overview'
]);

// 新增方法：隐藏系统概览
const hideSystemOverview = () => {
  showSystemOverview.value = false;
}

const handleNewConversation = () => {
  isNewConversation.value = true;
  activeConversationId.value = Date.now();
  showSystemOverview.value = true;
  currentMessages.value = [];
  nextTick(() => {
    conversationMain.value?.focusInput();
    conversationMain.value?.resetWelcome();
  });
};

const handleUpdateConversations = (newConversations) => {
  conversations.value = newConversations;
};

const handleRedirectToOverview = () => {
  router.push({ name: 'SystemOverview' })
}

const createConversation = (message) => {
  const newId = activeConversationId.value;
  const newName = message.trim() || `新对话${conversations.value.length + 1}`;
  
  conversations.value.push({
    id: newId,
    name: newName.length > 20 ? `${newName.substring(0, 20)}...` : newName,
    messages: [],
    hasShownWelcome: false
  });
  
  isNewConversation.value = false;
};

const selectConversation = (index) => {
  if (index >= 0 && index < conversations.value.length) {
    activeConversationId.value = conversations.value[index].id;
    showSystemOverview.value = conversations.value[index].messages.length === 0;
    currentMessages.value = [...conversations.value[index].messages];
    
    // 确保重置新对话状态
    if (isNewConversation.value) {
      isNewConversation.value = false;
    }
    
    // 滚动到底部并聚焦输入框
    nextTick(() => {
      conversationMain.value?.scrollToBottom();
      conversationMain.value?.focusInput();
    });
  }
};

// 确保对话存在的方法
const ensureConversationExists = (message) => {
  if (!activeConversationId.value) {
    activeConversationId.value = Date.now();
    const newName = message.length > 20 
      ? `${message.substring(0, 20)}...` 
      : message || `新对话${conversations.value.length + 1}`;
    
    const newConversation = {
      id: activeConversationId.value,
      name: newName,
      messages: [],
      hasShownWelcome: false
    };
    
    conversations.value.push(newConversation);
    
    // 触发创建对话事件
    emit('create-conversation', {
      id: activeConversationId.value,
      name: newName,
      initialMessage: message
    });
    
    // 确保选择新创建的对话
    selectConversation(conversations.value.length - 1);
    
    return true;
  }
  return false;
};

const handleSendMessage = async (message) => {
  // 1. 检查是否需要创建新对话
  if (!activeConversationId.value) {
    const newId = Date.now();
    const newName = message.length > 20 
      ? `${message.substring(0, 20)}...` 
      : message || `新对话${conversations.value.length + 1}`;
    
    // 通过侧边栏组件创建对话
    const newConversation = sidebar.value.ensureConversationExists(newId, message);
    
    // 更新主组件状态
    activeConversationId.value = newId;
    isNewConversation.value = true;
    showSystemOverview.value = false;
    currentMessages.value = [];
    
    // 触发创建对话事件
    emit('create-conversation', {
      id: newId,
      name: newName,
      initialMessage: message
    });
  }
  
  // 2. 添加用户消息
  const userMessage = {
    id: Date.now(),
    content: message,
    sender: 'user',
    timestamp: new Date()
  };
  currentMessages.value.push(userMessage);
  
  // 3. 更新对话消息
  const currentConversation = conversations.value.find(
    conv => conv.id === activeConversationId.value
  );
  
  if (currentConversation) {
    // 添加欢迎消息（如果是第一次）
    if (!currentConversation.hasShownWelcome) {
      const welcomeMessage = {
        id: Date.now() + 1,
        content: '您好！我是运维智能助手，请问有什么可以帮您？',
        sender: 'ai',
        timestamp: new Date()
      };
      currentMessages.value.push(welcomeMessage);
      currentConversation.hasShownWelcome = true;
    }
    
    currentConversation.messages = [...currentMessages.value];
    
    // 模拟AI回复
    setTimeout(() => {
      const aiResponse = {
        id: Date.now() + 2,
        content: getAIResponse(message),
        sender: 'ai',
        timestamp: new Date()
      };
      currentMessages.value.push(aiResponse);
      currentConversation.messages = [...currentMessages.value];
      nextTick(() => conversationMain.value?.scrollToBottom());
    }, 1000);
  }
  
  // 重置新对话状态
  isNewConversation.value = false;
};

const createNewConversation = (payload) => {
  const { id, name } = payload;
  
  // 避免重复创建
  if (!conversations.value.some(c => c.id === id)) {
    conversations.value.push({
      id,
      name: name.length > 20 ? `${name.substring(0, 20)}...` : name,
      messages: [],
      hasShownWelcome: false
    });
    
    // 选择新创建的对话
    selectConversation(conversations.value.length - 1);
  }
};

// 添加智能回复生成函数
const getAIResponse = (message) => {
  const responses = {
    "故障": "建议先检查系统日志，查看最近的错误记录。",
    "性能": "可以使用top或htop命令查看系统资源使用情况。",
    "网络": "请检查网络连接状态和防火墙设置。",
    "数据库": "建议检查数据库服务状态和连接字符串。"
  };
  
  for (const [keyword, response] of Object.entries(responses)) {
    if (message.includes(keyword)) {
      return response;
    }
  }
  
  return `已收到您关于"${message}"的问题，我们的系统正在分析处理中...`;
};
</script>

<style scoped>
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: Arial, sans-serif;
}

.app-container {
  display: flex;
  height: 100vh;
  width: 100vw;
  background-color: #f5f5f5;
}
.main-layout {
  display: flex;
  width: 100%;
  height: 100%;
}

.content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}
</style>