<template>
  <div class="conversation-sidebar" :class="{ 'collapsed': !isExpanded }">
    <!-- 侧边栏收起时的展开按钮 -->
    <div v-if="!isExpanded" class="expand-button" @click="toggleSidebar">
      <span class="iconfont icon-arrow-right-s-line"></span>
    </div>
    
    <div v-show="isExpanded" class="sidebar-content">
      <!-- 删除确认弹窗 -->
      <div v-if="showDeleteConfirm" class="delete-confirm-modal">
        <div class="delete-confirm-content">
          <p>确定要删除{{ deleteTarget.type === 'ticket' ? '工单' : '对话' }} "{{ deleteTarget.name }}" 吗？</p>
          <div class="confirm-buttons">
            <button class="confirm-btn cancel" @click="showDeleteConfirm = false">取消</button>
            <button class="confirm-btn" @click="confirmDelete">确认</button>
          </div>
        </div>
      </div>

      <div class="sidebar-header">
        <div class="platform-info">
          <span class="iconfont icon-weixin"> </span>
          <span class="platform-icon">智能体对话运维页</span>
          <button class="toggle-btn" @click="toggleSidebar">
            <span class="iconfont icon-arrow-left-s-line"></span>
          </button>
        </div>
      </div>

      <div class="conversation-actions">
        <button class="new-conversation" @click="handleNewConversation">
          <span class="iconfont icon-jia1"></span> 新对话
        </button>
      </div>

      <div class="tickets-section">
        <div class="tickets-header">
          <div 
            class="add-ticket-btn" 
            @click="showNewTicketInput = true"
            v-if="!showNewTicketInput"
          >
            <span class="iconfont icon-addFolder-fill"> 新工单 </span>
          </div>
          <input
            v-if="showNewTicketInput"
            ref="newTicketInput"
            v-model="newTicketName"
            @blur="createTicket"
            @keyup.enter="createTicket"
            placeholder="输入工单名称"
            class="ticket-input"
          >
        </div>

        <div 
          v-for="(ticket, index) in tickets" 
          :key="ticket.id" 
          class="ticket-item"
        >
          <div 
            class="ticket-header"
            @click="toggleTicket(index)"
          >
            <span class="ticket-icon iconfont" :class="ticket.isOpen ? 'icon-24gf-folderOpen' : 'icon-weidaqiadewenjianjia'"></span>
            
            <span 
              v-if="!ticket.editing"
              class="ticket-name"
              :title="ticket.name"
            >
              {{ ticket.name }}
            </span>
            
            <input
              v-else
              ref="ticketNameInputs"
              v-model="ticket.name"
              @blur="finishEditingTicket(index)"
              @keyup.enter="finishEditingTicket(index)"
              class="ticket-edit-input"
              @click.stop
              :title="ticket.name" 
            >
            
            <span 
              class="ticket-actions"
              @click.stop="toggleTicketMenu(index)"
            >
              <span class="iconfont icon-gengduo"></span>
              <div 
                v-if="ticket.showMenu" 
                class="ticket-menu"
                @click.stop
              >
                <div @click="startEditingTicket(index)">重命名</div>
                <div @click="prepareDelete('ticket', index)">删除</div>
              </div>
            </span>
          </div>
          
          <div 
            v-if="ticket.isOpen" 
            class="ticket-conversations"
          >
            <div 
              v-for="(conversation, cIndex) in ticket.conversations" 
              :key="conversation.id"
              class="conversation-subitem"
              :class="{ active: activeConversationId === conversation.id }"
              @click="selectConversation(conversation.id, 'ticket-conversation', index, cIndex)"
            >
              <span class="conversation-subitem-icon iconfont icon-duihuakuang"></span>
              
              <span 
                v-if="!conversation.editing"
                class="conversation-subitem-name"
                :title="conversation.name"
              >
                {{ conversation.name }}
              </span>
              
              <input
                v-else
                :ref="el => { if (el) conversationNameInputs[`${index}-${cIndex}`] = el }"
                v-model="conversation.name"
                @blur="finishEditingConversation(index, cIndex)"
                @keyup.enter="finishEditingConversation(index, cIndex)"
                class="conversation-edit-input"
                @click.stop
                :title="conversation.name"
              >
              
              <span 
                class="conversation-subitem-actions"
                @click.stop="toggleConversationMenu(index, cIndex)"
              >
                <span class="iconfont icon-gengduo"></span>
                <div 
                  v-if="conversation.showMenu" 
                  class="conversation-menu"
                  @click.stop
                >
                  <div @click="startEditingConversation(index, cIndex)">重命名</div>
                  <div @click="prepareDelete('ticket-conversation', index, cIndex)">删除</div>
                </div>
              </span>
            </div>
          </div>
        </div>
      </div>

      <div class="conversation-list">
        <div class="conversation-title">对话</div>
        <div 
          v-for="(item, index) in conversations" 
          :key="item.id" 
          class="conversation-item"
          :class="{ active: activeConversationId === item.id }"
          @click="selectConversation(item.id, 'conversation', index)"
        >
          <span class="conversation-item-name">{{ item.name }}</span>
          <span 
            class="conversation-item-actions"
            @click.stop="prepareDelete('conversation', index)"
          >
            <span class="iconfont icon-delete"></span>
          </span>
        </div>
      </div>

      <div class="user-info">
        <div class="user-avatar">👤</div>
        <div class="user-details">
          <div class="user-name">用户001</div>
          <button class="settings-btn" @click="openSettings">
            <span class="iconfont icon-shezhi"></span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onBeforeUnmount, watch } from 'vue'
import { reactive } from 'vue'

const props = defineProps({
  conversations: {
    type: Array,
    required: true  // 改为required确保数据存在
  },
  activeConversationId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits([
  'new-conversation-click', 
  'conversation-selected', 
  'create-conversation',
  'update:conversations',
  'redirect-to-overview' 
])

const isExpanded = ref(true)
const activeItem = ref({ 
  type: null, 
  index: null,
  ticketIndex: null,
  conversationIndex: null 
})

// 修改为使用reactive确保响应式
const tickets = ref([
  { 
    id: 1,
    name: '工单1',
    isOpen: true,
    showMenu: false,
    editing: false,
    conversations: [
      { id: 3, name: '对话1', showMenu: false, editing: false },
      { id: 4, name: '对话2', showMenu: false, editing: false }
    ]
  },
  { 
    id: 2,
    name: '工单2',
    isOpen: false,
    showMenu: false,
    editing: false,
    conversations: [
      { id: 5, name: '对话3', showMenu: false, editing: false }
    ]
  }
])

const showNewTicketInput = ref(false)
const newTicketName = ref('')
const showDeleteConfirm = ref(false)
const deleteTarget = ref({
  type: null,
  index: null,
  ticketIndex: null,
  conversationIndex: null,
  name: '',
  id: null
})

const newTicketInput = ref(null)
const ticketNameInputs = ref([])
const conversationNameInputs = ref([])

const handleNewConversation = () => {
  emit('new-conversation-click')
}

const createNewConversation = (initialMessage = '') => {
  const newId = Date.now()
  const newName = initialMessage 
    ? initialMessage.length > 15 
      ? initialMessage.substring(0, 15) + '...' 
      : initialMessage
    : `新对话${props.conversations.length + 1}`
  const newConversation = { 
    id: newId, 
    name: newName
  }
  
  const updatedConversations = [...props.conversations, newConversation]
  emit('update:conversations', updatedConversations)
  return newConversation
}

const closeAllMenus = () => {
  tickets.value.forEach(ticket => {
    ticket.showMenu = false
    ticket.conversations.forEach(conv => {
      conv.showMenu = false
    })
  })
}

const toggleSidebar = () => {
  isExpanded.value = !isExpanded.value
}

const selectConversation = (conversationId, type, index, conversationIndex = null) => {
  if (type === 'ticket-conversation') {
    activeItem.value = { 
      type, 
      ticketIndex: index,
      conversationIndex 
    }
  } else {
    activeItem.value = { 
      type, 
      index,
      ticketIndex: type === 'ticket' ? index : null
    }
  }
  
  emit('conversation-selected', {
    id: conversationId,
    type,
    index,
    conversationIndex
  })
}

const toggleTicket = (index) => {
  const ticket = tickets.value[index]
  ticket.isOpen = !ticket.isOpen
  ticket.showMenu = false
}

const toggleTicketMenu = (index) => {
  tickets.value.forEach((ticket, i) => {
    ticket.showMenu = i === index ? !ticket.showMenu : false
  })
}

const toggleConversationMenu = (ticketIndex, conversationIndex) => {
  tickets.value[ticketIndex].conversations.forEach((conv, i) => {
    conv.showMenu = i === conversationIndex ? !conv.showMenu : false
  })
}

const startEditingTicket = (index) => {
  tickets.value[index].editing = true
  tickets.value[index].showMenu = false
  nextTick(() => {
    ticketNameInputs.value[index].focus()
  })
}

const startEditingConversation = (ticketIndex, conversationIndex) => {
  tickets.value[ticketIndex].conversations[conversationIndex].editing = true
  tickets.value[ticketIndex].conversations[conversationIndex].showMenu = false
  nextTick(() => {
    conversationNameInputs.value[`${ticketIndex}-${conversationIndex}`].focus()
  })
}

const finishEditingTicket = (index) => {
  tickets.value[index].editing = false
}

const finishEditingConversation = (ticketIndex, conversationIndex) => {
  tickets.value[ticketIndex].conversations[conversationIndex].editing = false
}

const prepareDelete = (type, index, conversationIndex = null) => {
  if (type === 'ticket') {
    const ticket = tickets.value[index]
    deleteTarget.value = {
      type: 'ticket',
      index,
      name: ticket.name,
      id: ticket.id
    }
  } else if (type === 'conversation') {
    const conversation = props.conversations[index]
    deleteTarget.value = {
      type: 'conversation',
      index,
      name: conversation.name,
      id: conversation.id
    }
  } else if (type === 'ticket-conversation') {
    const conversation = tickets.value[index].conversations[conversationIndex]
    deleteTarget.value = {
      type: 'ticket-conversation',
      ticketIndex: index,
      conversationIndex,
      name: conversation.name,
      id: conversation.id
    }
  }
  
  showDeleteConfirm.value = true
  closeAllMenus()
}

const confirmDelete = async () => {
  try {
    const wasActiveConversation = deleteTarget.value.id === props.activeConversationId
    
    if (deleteTarget.value.type === 'ticket') {
      tickets.value.splice(deleteTarget.value.index, 1)
    } else if (deleteTarget.value.type === 'conversation') {
      const updatedConversations = props.conversations.filter((_, i) => i !== deleteTarget.value.index)
      emit('update:conversations', updatedConversations)
    } else if (deleteTarget.value.type === 'ticket-conversation') {
      const ticketIndex = deleteTarget.value.ticketIndex
      const conversationIndex = deleteTarget.value.conversationIndex
      tickets.value[ticketIndex].conversations.splice(conversationIndex, 1)
    }
    
    // 如果删除的是当前活跃对话
    if (wasActiveConversation) {
      // 取消选中状态
      emit('conversation-selected', { id: null })
      // 触发路由重定向
      emit('redirect-to-overview')
    }
    
  } catch (error) {
    console.error('删除失败:', error)
  } finally {
    // 重置删除状态
    showDeleteConfirm.value = false
    deleteTarget.value = {
      type: null,
      index: null,
      ticketIndex: null,
      conversationIndex: null,
      name: '',
      id: null
    }
  }
}

const createTicket = () => {
  if (newTicketName.value.trim()) {
    const newId = Math.max(...tickets.value.map(t => t.id), 0) + 1
    tickets.value.push({
      id: newId,
      name: newTicketName.value.trim(),
      isOpen: false,
      showMenu: false,
      editing: false,
      conversations: []
    })
    newTicketName.value = ''
  }
  showNewTicketInput.value = false
}

const openSettings = () => {
  window.open('/settings', '_blank')
}

onMounted(() => {
  document.addEventListener('click', closeAllMenus)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', closeAllMenus)
})

watch(showNewTicketInput, (val) => {
  if (val) {
    nextTick(() => {
      newTicketInput.value.focus()
    })
  }
})

defineExpose({
  createNewConversation,
  selectConversation
})
</script>

<style scoped>
/* 原有样式保持不变，只添加新增的样式 */
.conversation-subitem-icon {
  margin-right: 8px;
  font-size: 14px;
  color: #666;
}

.conversation-menu {
  position: absolute;
  right: 0;
  top: 100%;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 2;
  min-width: 100px;
}

.conversation-menu div {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 13px;
}

.conversation-menu div:hover {
  background-color: #f5f5f5;
}

.conversation-subitem-actions {
  position: relative;
  flex-shrink: 0;
  padding: 0 4px;
  cursor: pointer;
  color: #666;
  visibility: hidden;
}

.conversation-subitem:hover .conversation-subitem-actions,
.conversation-subitem.active .conversation-subitem-actions {
  visibility: visible;
}

/* 其余原有样式保持不变 */
.conversation-sidebar {
  width: 240px;
  height: 100vh;
  background-color: rgb(240, 240, 240);
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  position: relative;
  transition: width 0.3s ease;
}

.conversation-sidebar.collapsed {
  width: 0;
  border-right: none;
}

.expand-button {
  position: absolute;
  right: -20px;
  top: 20px;
  width: 20px;
  height: 40px;
  background-color: white;
  border: 1px solid #e8e8e8;
  border-left: none;
  border-radius: 0 4px 4px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
  padding: 16px;
}

.platform-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.platform-icon {
  font-weight: bold;
  white-space: nowrap;
}

.platform-info .icon-weixin{
  font-size: 30px;
  padding: 0 8px;
}

.toggle-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0 8px;
}

.sidebar-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 16px;
  overflow-y: auto;
  height: 100%;
  position: relative;
}

.conversation-actions {
  margin-bottom: 16px;
}

.new-conversation {
  padding: 8px 12px;
  border: 1px solid #878787;
  color: #3b3b3b;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  width: 100%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  transition: background-color 0.2s;
}

.new-conversation:hover {
  background-color: #878787;
  color: white;
}

.new-conversation .iconfont {
  font-size: 20px;
}

.conversation-list {
  margin-bottom: 16px;
}

.conversation-title {
  padding: 8px 0;
  color: #878787;
  font-size: 14px;
}

.conversation-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  border-radius: 4px;
  margin-bottom: 4px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conversation-item:hover {
  background-color: #f5f5f5;
}

.conversation-item.active {
  background-color: #dedede;
  color: #353535;
}

.conversation-item-name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.conversation-item-actions {
  flex-shrink: 0;
  padding: 0 4px;
  cursor: pointer;
  color: #666;
  visibility: hidden;
}

.conversation-item:hover .conversation-item-actions,
.conversation-item.active .conversation-item-actions {
  visibility: visible;
}

.tickets-section {
  margin-top: 16px;
  border-top: 1px solid #e8e8e8;
  padding-top: 16px;
}

.tickets-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.add-ticket-btn {
  cursor: pointer;
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.add-ticket-btn:hover {
  background-color: #d2d2d2;
}

.ticket-input {
  width: 100%;
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
}

.ticket-input:focus {
  border-color: #0d0056;
}

.ticket-item {
  margin-bottom: 8px;
  border-radius: 4px;
}

.ticket-header {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  background-color: #fafafa;
  position: relative;
}

.ticket-header:hover {
  background-color: #d8d8d8;
}

.ticket-icon {
  margin-right: 8px;
  font-size: 14px;
  width: 16px;
  text-align: center;
  color: #666;
}

.ticket-name {
  flex: 1;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

.ticket-edit-input {
  flex: 1;
  font-size: 14px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 2px 8px;
  outline: none;
  max-width: calc(100% - 50px);
  box-sizing: border-box; 
  min-width: 0;
}

.ticket-edit-input:focus {
  border-color: #1a1355;
}

.ticket-actions {
  flex-shrink: 0;
  padding: 0 8px;
  cursor: pointer;
  position: relative;
  user-select: none;
  color: #666;
}

.ticket-menu {
  position: absolute;
  right: 0;
  top: 100%;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 2;
  min-width: 100px;
}

.ticket-menu div {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 13px;
}

.ticket-menu div:hover {
  background-color: #f5f5f5;
}

.ticket-conversations {
  margin-left: 24px;
  padding: 4px 0;
}

.conversation-subitem {
  padding: 6px 12px;
  font-size: 13px;
  cursor: pointer;
  border-radius: 4px;
  margin: 2px 0;
  display: flex;
  align-items: center;
}

.conversation-subitem:hover {
  background-color: #f5f5f5;
}

.conversation-subitem.active {
  background-color: #dedede;
  color: #353535;
}

.conversation-subitem-name {
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-info {
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  font-size: 16px;
}

.user-details {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-name {
  font-size: 14px;
}

.user-details .iconfont{
  font-size: 18px;
}

.settings-btn {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  color: #666;
}

.delete-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 100;
}

.delete-confirm-content {
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  width: 300px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.delete-confirm-content p {
  margin-bottom: 20px;
  font-size: 16px;
  text-align: center;
}

.confirm-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.confirm-btn {
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  border: none;
  font-size: 14px;
}

.confirm-btn.cancel {
  background-color: #f5f5f5;
  color: #666;
}

.confirm-btn {
  background-color: #ff4d4f;
  color: white;
}

.confirm-btn:hover {
  opacity: 0.8;
}

.conversation-edit-input {
  flex: 1;
  font-size: 13px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 2px 8px;
  outline: none;
  max-width: calc(100% - 50px);
  box-sizing: border-box;
  min-width: 0;
  height: 24px;
  line-height: 20px;
}

.conversation-edit-input:focus {
  border-color: #393942;
}
</style>