<script setup>
import { ref, watch, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import ConversationMain from '@/components/ConversationMain.vue'

const route = useRoute()
const conversationId = ref(route.params.id)
const messages = ref([])
const isLoading = ref(false)

// 加载对话数据（统一数据结构）
const loadConversation = () => {
  const savedData = JSON.parse(localStorage.getItem('conversationData') || '{"conversations":[],"tickets":[]}')

  // 统一搜索逻辑
  let conversation = savedData.conversations.find(c => c.id === conversationId.value)
  if (!conversation) {
    savedData.tickets.forEach(ticket => {
      const found = ticket.conversations?.find(c => c.id === conversationId.value)
      if (found) conversation = found
    })
  }

  messages.value = conversation?.messages || []
}

// 保存对话数据（统一格式）
const saveConversation = () => {
  const savedData = JSON.parse(localStorage.getItem('conversationData') || '{"conversations":[],"tickets":[]}')

  // 更新逻辑...
  localStorage.setItem('conversationData', JSON.stringify(savedData))
}

// 处理发送消息
const handleSendMessage = (content) => {
  const newMessage = {
    id: Date.now(),
    sender: 'user',
    content,
    timestamp: new Date()
  }

  messages.value.push(newMessage)
  saveConversation()

  // 模拟AI回复
  simulateAIResponse(content)
}

// 模拟AI回复
const simulateAIResponse = (question) => {
  isLoading.value = true

  setTimeout(() => {
    const response = {
      id: Date.now(),
      sender: 'ai',
      content: `这是关于"${question}"的模拟回复`,
      timestamp: new Date()
    }

    messages.value.push(response)
    saveConversation()
    isLoading.value = false
  }, 1500)
}

// 监听路由变化
watch(() => route.params.id, (newId) => {
  conversationId.value = newId
  loadConversation()
})

// 初始化加载
onMounted(loadConversation)
</script>

<template>
  <ConversationMain
    :messages="messages"
    :isLoading="isLoading"
    @send-message="handleSendMessage"
  />
</template>
