<template>
  <div class="conversation-container">
    <!-- 新增的问号帮助按钮 -->
    <button class="help-button" @click="showHelp" v-if="showHelpButton">
      <i class="iconfont icon-wenhao"></i>
    </button>
    <!-- 主内容区 -->
    <div class="main-content">
      <div class="message-list">
        <div class="time-indicator" v-if="messages.length > 0">
          {{ currentTime }}
        </div>
        <!-- 消息列表内容 -->
        <div v-for="(msg, index) in messages" :key="index" class="message-bubble" :class="msg.sender">
          <div class="message-content">{{ msg.content }}</div>
          <!-- <div class="message-time">{{ msg.time }}</div> -->
          <div v-if="msg.sender === 'ai' && msg.isLoading" class="typing-indicator">
            <span></span>
            <span></span>
            <span></span>
          </div>
        </div>
      </div>

      <!-- 推荐问题 -->
      <RecommendedQuestions class="recommended-questions" @question-click="handleRecommendedQuestion" />


      <!-- 输入区域 -->
      <div class="input-area">
        <div class="input-box">
          <textarea ref="messageInput" v-model="message" placeholder="这里是对话输入框，向AI询问任何问题吧！"
            @input="adjustTextareaHeight" @keydown.enter.prevent="sendMessage"></textarea>

          <div class="bottom-controls">
            <div class="toolbar">
              <button class="tool-button" title="上传文件">
                <i class="iconfont icon-jia"></i>
              </button>
              <div class="tool-dropdown" ref="toolDropdown">
                <button class="tool-button" title="工具聚合" @click.stop="toggleTools">
                  <i class="iconfont icon-a-13Jtiaojie"></i>
                </button>
                <div v-if="showTools" class="dropdown-menu">
                  <button @click.stop="selectTool('search')">联网搜索</button>
                  <button @click.stop="selectTool('deep-think')">深度思考</button>
                </div>
              </div>
              <div class="tool-button-container" ref="phrasesPopup">
                <button class="tool-button" title="常用语" @click.stop="togglePhrases">
                  <i class="iconfont icon-cbox-full"></i>
                </button>

                <div v-if="showPhrases" class="phrases-popup">
                  <div class="phrases-header">
                    <h4>常用运维短语</h4>
                    <button class="close-btn" @click.stop="showPhrases = false">
                      <i class="iconfont icon-guanbi"></i>
                    </button>
                  </div>
                  <div class="phrases-list">
                    <div v-for="(phrase, index) in commonPhrases" :key="index" class="phrase-item"
                      @click.stop="handlePhraseClick(phrase)">
                      {{ phrase }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <button class="send-button" :class="{ disabled: !message.trim() }" @click="sendMessage">
              <i class="icon icon-send-up"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 推荐问题 - 放在输入区域下方 -->
      <!-- <RecommendedQuestions class="recommended-questions" @question-click="handleRecommendedQuestion" /> -->

      <!-- 页脚 -->
      <div class="footer" v-if="showSystemOverview">
        <p class="ai-info">请检查AI生成内容</p>
        <p class="copyright">Copyright © 2025. 新华-麒麟软件团队</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, computed, watch, onUnmounted } from 'vue'
import RecommendedQuestions from './RecommendedQuestions.vue'

const props = defineProps({
  isNewConversation: {
    type: Boolean,
    default: false
  },
  shouldShowWelcome: {
    type: Boolean,
    default: false
  },
  currentConversationId: {
    type: String,
    default: null
  },
  conversations: {
    type: Array,
    default: () => []
  },
  showSystemOverview: {
    type: Boolean,
    default: true
  },
  showSystemOverview: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits([
  'create-conversation',
  'message-sent',
  'hide-overview',
  'update-conversation'
])

const messageInput = ref(null)
const message = ref('')
const showTools = ref(false)
const showPhrases = ref(false)
const selectedTool = ref(null)
const messages = ref([])
const hasShownWelcome = ref(false)
const toolDropdown = ref(null)

// 精确的点击外部处理
const handleClickOutside = (event) => {
  const target = event.target
  
  // 工具聚合菜单关闭条件
  const isToolsRelated = toolDropdown.value?.contains(target) && 
                       (target.closest('.tool-button') || target.closest('.dropdown-menu'))
  
  if (!isToolsRelated) {
    showTools.value = false
  }
  
  // 常用语菜单关闭条件
  const isPhrasesRelated = phrasesPopup.value?.contains(target) && 
                         (target.closest('.tool-button') || target.closest('.phrases-popup'))
  
  if (!isPhrasesRelated) {
    showPhrases.value = false
  }
}

// 添加全局点击监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// 常用语数据
const commonPhrases = ref([
  "服务器宕机了怎么办？",
  "如何检查网络连接问题？",
  "数据库连接失败怎么解决？",
  "系统性能突然下降如何排查？",
  "如何查看系统日志？",
  "防火墙配置出错了怎么办？",
  "磁盘空间不足如何处理？",
  "服务无法启动如何调试？"
])

// 常用语点击处理
const togglePhrases = (event) => {
  event.stopPropagation()
  showTools.value = false
  showPhrases.value = !showPhrases.value
}

const handlePhraseClick = (phrase) => {
  // 获取当前光标位置
  const textarea = messageInput.value
  const startPos = textarea.selectionStart
  const endPos = textarea.selectionEnd
  const currentValue = message.value

  // 在光标位置插入短语
  message.value =
    currentValue.substring(0, startPos) +
    phrase +
    currentValue.substring(endPos)

  // 关闭弹出框并保持焦点
  showPhrases.value = false
  nextTick(() => {
    // 设置光标位置到插入内容之后
    const newCursorPos = startPos + phrase.length
    textarea.focus()
    textarea.setSelectionRange(newCursorPos, newCursorPos)
    adjustTextareaHeight()
  })
}

// 添加showHelpButton计算属性
const showHelpButton = computed(() => props.showSystemOverview)

// 计算当前时间
const currentTime = computed(() => {
  return new Date().toLocaleTimeString()
})

// 加载对话内容
const loadConversation = () => {
  return props.conversations.find(c => String(c.id) === props.currentConversationId)?.messages || [];
};

// 当前消息
const currentMessages = ref(loadConversation());


// 监听当前对话ID变化，加载对应对话内容
watch(() => props.currentConversationId, () => {
  currentMessages.value = loadConversation();
}, { immediate: true });

// 模拟AI回复
const getAIResponse = (question) => {
  const responses = {
    "服务器宕机": "建议先检查电源和网络连接，然后查看系统日志定位问题原因。",
    "网络问题": "请使用ping和traceroute命令测试网络连通性，检查防火墙设置。",
    "数据库连接": "检查数据库服务是否运行，验证连接字符串和权限设置。",
    "性能下降": "推荐使用top命令查看CPU和内存使用情况，分析进程资源占用。"
  }

  for (const [key, value] of Object.entries(responses)) {
    if (question.includes(key)) {
      return value
    }
  }

  return `已收到您关于"${question}"的问题，我们的系统正在分析处理中...`
}

const toggleTools = (event) => {
  event.stopPropagation()
  showPhrases.value = false
  showTools.value = !showTools.value
}

const selectTool = (tool) => {
  selectedTool.value = tool
  showTools.value = false
}

const adjustTextareaHeight = () => {
  const textarea = messageInput.value
  textarea.style.height = 'auto'
  const maxHeight = 3 * parseInt(getComputedStyle(textarea).lineHeight)
  textarea.style.height = `${Math.min(textarea.scrollHeight, maxHeight)}px`

  if (textarea.scrollHeight > maxHeight) {
    textarea.style.overflowY = 'auto'
  } else {
    textarea.style.overflowY = 'hidden'
  }
}

const sendMessage = () => {
  const userMessageContent = message.value.trim()
  if (!userMessageContent) return

  // 通知父组件消息已发送（这会触发隐藏SystemOverview）
  emit('message-sent', userMessageContent)

  // 新增：立即通知父组件隐藏SystemOverview
  emit('hide-overview')

  // 保存用户消息
  const userMessage = {
    id: Date.now(),
    sender: 'user',
    content: userMessageContent,
    // time: new Date().toLocaleTimeString(),
    isLoading: false
  }
  messages.value.push(userMessage)

  const updatedMessages = [...currentMessages.value, userMessage];
  currentMessages.value = updatedMessages;

  // 查找完整对话对象
  const conversation = props.conversations.find(c => String(c.id) === props.currentConversationId);

  if (conversation) {
    const updatedConversation = {
      ...conversation,
      messages: updatedMessages
    };
    emit('update-conversation', updatedConversation);
  }

  // 如果是新对话，通知父组件创建对话
  if (props.isNewConversation) {
    emit('create-conversation', userMessageContent)
  }

  // 清空输入框
  message.value = ''
  nextTick(() => {
    messageInput.value.style.height = 'auto'
    scrollToBottom()
  })

  // 只在第一次发送消息时显示欢迎消息
  if (!hasShownWelcome.value) {
    const welcomeMessage = {
      sender: 'ai',
      content: '您好！我是运维智能助手，请问有什么可以帮您？',
      // time: new Date().toLocaleTimeString(),
      isLoading: false
    }
    messages.value.push(welcomeMessage)
    hasShownWelcome.value = true

    // 更新对话记录
    if (!props.isNewConversation) {
      emit('update-conversation', {
        id: props.currentConversationId,
        messages: [...messages.value]
      })
    }

    scrollToBottom()
  }

  // 模拟AI回复延迟
  setTimeout(() => {
    const aiResponse = {
      sender: 'ai',
      content: getAIResponse(userMessageContent),
      time: new Date().toLocaleTimeString(),
      isLoading: false
    }
    messages.value.push(aiResponse)

    // 更新对话记录
    if (!props.isNewConversation) {
      emit('update-conversation', {
        id: props.currentConversationId,
        messages: [...messages.value]
      })
    }

    scrollToBottom()
  }, 1500)
}

const handleRecommendedQuestion = (question) => {
  message.value = question
  nextTick(() => {
    sendMessage()
  })
}

const scrollToBottom = () => {
  nextTick(() => {
    const container = document.querySelector('.message-list')
    if (container) {
      container.scrollTop = container.scrollHeight
    }
  })
}

const showHelp = () => {
  console.log("显示帮助信息")
  alert("这里是帮助信息。")
}

const focusInput = () => {
  messageInput.value.focus()
}

// 移除了onMounted中的欢迎消息初始化

const resetWelcome = () => {
  hasShownWelcome.value = false
}

defineExpose({
  focusInput,
  resetWelcome
})
</script>

<style scoped>
/* 新增时间指示器样式 */
.time-indicator {
  text-align: center;
  color: #999;
  font-size: 12px;
  margin: 10px 0;
  padding: 4px 0;
}

/* 保持原有样式不变 */
.conversation-container {
  display: flex;
  height: 100vh;
  background-color: #fff;
  flex-direction: column;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  /* 修复flex布局滚动问题 */
}

.help-button {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 100;
  transition: all 0.2s;
}

.help-button .iconfont {
  font-size: 30px;
}

.message-list {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  padding-bottom: 60px;
}

.input-area {
  padding: 16px;
  background: white;
}

.input-box {
  border: 1px solid #d9d9d9;
  border-radius: 16px;
  padding: 12px;
  background: #f9f9f9;
}

.input-box textarea {
  width: 100%;
  min-height: 100px;
  max-height: 200px;
  border: none;
  resize: none;
  font-size: 14px;
  line-height: 1.5;
  padding: 8px;
  background: white;
  border-radius: 8px;
  margin-bottom: 8px;
}

.input-box textarea:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(81, 81, 81, 0.2);
}

.bottom-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.toolbar {
  display: flex;
  gap: 8px;
}

.tool-button {
  width: 32px;
  height: 32px;
  background: none;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.tool-button:hover {
  background-color: #e8e8e8;
}

.tool-dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  bottom: 100%;
  left: 0;
  background: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 120px;
  overflow: hidden;
}

.dropdown-menu button {
  width: 100%;
  padding: 8px 12px;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
}

.dropdown-menu button:hover {
  background-color: #f5f5f5;
}

.send-button {
  width: 36px;
  height: 36px;
  background-color: #333;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.send-button:hover {
  background-color: #4a4949;
}

.send-button.disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

.recommended-questions {
  padding: 0 16px;
  background: white;
  border-bottom: none !important;
}

.footer {
  padding: 12px 16px;
  text-align: center;
  font-size: 12px;
  color: #999;
  background: white;
}

.ai-info {
  margin-top: 4px;
}

.icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.icon-send-up {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ffffff'%3E%3Cpath d='M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z'/%3E%3C/svg%3E");
  transform: rotate(-90deg);
}

.message-bubble {
  min-width: 60px;
  max-width: 85%;
  padding: 12px 16px;
  width: fit-content;
  margin-bottom: 12px;
  border-radius: 18px;
  position: relative;
  word-wrap: break-word;
}

.message-bubble.user {
  background: #1890ff;
  color: white;
  margin-left: auto;
  border-bottom-right-radius: 4px;
}

.message-bubble.ai {
  background: #f0f0f0;
  margin-right: auto;
  border-bottom-left-radius: 4px;
  padding: 10px 18px 10px 14px;
}

.message-content {
  margin-bottom: 4px;
  line-height: 1.5;
  white-space: pre-wrap;
}

.message-bubble:has(.message-content:empty),
.message-bubble:has(.message-content:blank),
.message-bubble:has(.typing-indicator) {
  min-width: 80px;
  padding: 12px 16px;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  text-align: right;
}

.typing-indicator {
  display: flex;
  padding: 8px;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  margin: 0 2px;
  background: #999;
  border-radius: 50%;
  opacity: 0.4;
  animation: typingAnimation 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typingAnimation {

  0%,
  60%,
  100% {
    transform: translateY(0);
    opacity: 0.4;
  }

  30% {
    transform: translateY(-5px);
    opacity: 1;
  }
}

/* 修改后的常用语样式 */
.tool-button-container {
  position: relative;
  display: inline-block;
}

.phrases-popup {
  position: absolute;
  bottom: auto;
  top: -500%;
  left: 100%;
  /* 改为右侧显示 */
  margin-left: 10px;
  /* 与按钮间距 */
  width: 280px;
  max-height: 400px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

/* 确保在小屏幕上也能显示完整 */
@media (max-width: 768px) {
  .phrases-popup {
    left: auto;
    right: 0;
    margin-left: 0;
    margin-right: 10px;
  }
}

.phrases-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.phrases-header h4 {
  margin: 0;
  font-size: 14px;
  color: #374151;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  color: #6b7280;
}

.phrases-list {
  padding: 8px 0;
  max-height: 140px;
  overflow-y: auto;
}

.phrase-item {
  padding: 10px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;
  transition: background-color 0.2s;
}

.phrase-item:hover {
  background-color: #f3f4f6;
}
</style>