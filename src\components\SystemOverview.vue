<template>
  <div class="system-overview">
    <div class="header">
      <FaultHandling />
    </div>

    <div class="dashboard">
      <!-- 系统指标卡片 -->
      <div class="metrics-card">
        <h2>系统指标</h2>
        <div class="metrics-grid">
          <!-- CPU指标盒子 -->
          <div class="metric-box">
            <div class="progress-ring cpu-ring">
              <svg width="120" height="120" viewBox="0 0 100 100">
                <!-- 背景圆环：宽度8，浅灰色#ECECEC -->
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  stroke="#ECECEC"
                  stroke-width="8"
                  fill="none"
                />
                <!-- 进度圆环：宽度8，红色#FF3B30 -->
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  stroke="#FF3B30"
                  stroke-width="8"
                  fill="none"
                  :stroke-dasharray="circumference"
                  :stroke-dashoffset="getDashOffset(metrics.cpu.usage)"
                  transform="rotate(-90 50 50)"
                />
                <!-- CPU文字：调整位置和大小 -->
                <text
                  x="50"
                  y="45"
                  text-anchor="middle"
                  dominant-baseline="middle"
                  font-size="16"
                  font-weight="bold"
                  fill="#333"
                >CPU</text>
                <!-- 百分比文字：调整位置和大小，增大间距 -->
                <text
                  x="50"
                  y="65"
                  text-anchor="middle"
                  font-size="14"
                  fill="#666"
                >{{ metrics.cpu.usage }}%</text>
              </svg>
            </div>
          </div>

          <!-- 内存指标盒子 -->
          <div class="metric-box">
            <div class="progress-ring memory-ring">
              <svg width="120" height="120" viewBox="0 0 100 100">
                <!-- 背景圆环：宽度8，浅灰色#ECECEC -->
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  stroke="#ECECEC"
                  stroke-width="8"
                  fill="none"
                />
                <!-- 进度圆环：宽度8，蓝色#007AFF -->
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  stroke="#007AFF"
                  stroke-width="8"
                  fill="none"
                  :stroke-dasharray="circumference"
                  :stroke-dashoffset="getDashOffset(metrics.memory.usage)"
                  transform="rotate(-90 50 50)"
                />
                <!-- 内存文字：调整位置和大小 -->
                <text
                  x="50"
                  y="45"
                  text-anchor="middle"
                  dominant-baseline="middle"
                  font-size="16"
                  font-weight="bold"
                  fill="#333"
                >内存</text>
                <!-- 百分比文字：调整位置和大小，增大间距 -->
                <text
                  x="50"
                  y="65"
                  text-anchor="middle"
                  font-size="14"
                  fill="#666"
                >{{ metrics.memory.usage }}%</text>
              </svg>
            </div>
          </div>

          <!-- 网络指标盒子 -->
          <div class="metric-box">
            <div class="progress-ring network-ring">
              <svg width="120" height="120" viewBox="0 0 100 100">
                <!-- 背景圆环：宽度8，浅灰色#ECECEC -->
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  stroke="#ECECEC"
                  stroke-width="8"
                  fill="none"
                />

                <!-- 上传部分（左半圆）：蓝色#007AFF -->
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  stroke="#007AFF"
                  stroke-width="8"
                  fill="none"
                  :stroke-dasharray="halfCircumference"
                  :stroke-dashoffset="getNetworkDashOffset(metrics.network.upload)"
                  transform="rotate(-90 50 50)"
                />

                <!-- 下载部分（右半圆）：紫色#7F56D9 -->
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  stroke="#7F56D9"
                  stroke-width="8"
                  fill="none"
                  :stroke-dasharray="halfCircumference"
                  :stroke-dashoffset="getNetworkDashOffset(metrics.network.download)"
                  transform="rotate(90 50 50)"
                />

                <!-- 上传文字：调整位置和大小，增大间距 -->
                <text
                  x="35"
                  y="44"
                  text-anchor="middle"
                  dominant-baseline="middle"
                  font-size="12"
                  font-weight="bold"
                  fill="#333"
                >上传</text>
                <text
                  x="35"
                  y="63"
                  text-anchor="middle"
                  font-size="12"
                  fill="#666"
                >{{ metrics.network.upload }}M</text>

                <!-- 下载文字：调整位置和大小，增大间距 -->
                <text
                  x="65"
                  y="44"
                  text-anchor="middle"
                  dominant-baseline="middle"
                  font-size="12"
                  font-weight="bold"
                  fill="#333"
                >下载</text>
                <text
                  x="65"
                  y="63"
                  text-anchor="middle"
                  font-size="12"
                  fill="#666"
                >{{ metrics.network.download }}M</text>

              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统事件记录卡片 -->
      <div class="events-card">
        <h2>系统事件记录</h2>
        <div 
          class="event-item"
          v-for="(event, index) in systemEvents"
          :key="index"
        >
          <span class="event-name">{{ event.name }}</span>
          <span class="event-time">{{ event.time }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import FaultHandling from './FaultHandling.vue'

// 圆周计算
const radius = 40
const circumference = 2 * Math.PI * radius
const halfCircumference = circumference / 2

// 系统指标数据
const metrics = ref({
  cpu: {
    usage: 0
  },
  memory: {
    usage: 0
  },
  network: {
    upload: 0,
    download: 0
  }
})

// 系统事件数据
const systemEvents = ref([])

// 计算圆环的dashoffset
const getDashOffset = (usage) => {
  return circumference - (circumference * usage) / 100
}

// 计算网络圆环的dashoffset (假设最大值为30M)
const getNetworkDashOffset = (value) => {
  const maxValue = 30 // 假设最大值为30M
  return halfCircumference - (halfCircumference * Math.min(value, maxValue)) / maxValue
}

// 模拟从API获取数据
const fetchSystemMetrics = async () => {
  // 模拟API请求延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // 模拟返回的数据
  return {
    cpu: {
      usage: 100
    },
    memory: {
      usage: 61
    },
    network: {
      upload: 6,
      download: 20
    }
  }
}

// 模拟从API获取系统事件
const fetchSystemEvents = async () => {
  // 模拟API请求延迟
  await new Promise(resolve => setTimeout(resolve, 300))
  
  // 模拟返回的数据
  return [
    { name: 'Apache服务重启', time: '2025.07.21' },
    { name: 'MySQL写数据失败', time: '2025.07.21' },
    { name: 'mq磁盘已满', time: '2025.07.20' }
  ]
}

// 组件挂载时获取数据
onMounted(async () => {
  try {
    const [metricsData, eventsData] = await Promise.all([
      fetchSystemMetrics(),
      fetchSystemEvents()
    ])
    
    metrics.value = metricsData
    systemEvents.value = eventsData
  } catch (error) {
    console.error('获取系统数据失败:', error)
  }
})
</script>

<style scoped>
.system-overview {
  padding: 20px;
  background-color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
  font-family: "PingFang SC", "Microsoft YaHei", sans-serif;
}

.header {
  margin-bottom: 20px;
}

.dashboard {
  display: flex;
  gap: 20px;
}

/* 加深系统指标卡片阴影 */
.metrics-card {
  flex: 1;
  border: 1px solid #F5F5F5;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); /* 阴影更明显 */
}

/* 加深系统事件记录卡片阴影 */
.events-card {
  flex: 1;
  border: 1px solid #F5F5F5;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1); /* 阴影更明显 */
}

h2 {
  font-size: 18px;
  text-align: center;
  margin-bottom: 16px;
  color: #333;
}

.metrics-grid {
  display: flex;
  justify-content: space-around;
  gap: 10px;
}

.metric-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  border: none;
  box-shadow: none;
}

.progress-ring {
  margin-bottom: 10px;
}

.progress-ring svg {
  transform: scale(1);
}

.event-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #F5F5F5;
  font-size: 14px;
}

.event-name {
  color: #333;
}

.event-time {
  color: #999;
  font-size: 13px;
}

/* 颜色精确匹配设计图 */
.cpu-ring circle:nth-child(2) {
  stroke: #FF3B30;
}

.memory-ring circle:nth-child(2) {
  stroke: #007AFF;
}

.network-ring circle:nth-child(2) {
  stroke: #007AFF;
}

.network-ring circle:nth-child(3) {
  stroke: #7F56D9;
}
</style>