import axios from 'axios'

// 创建axios实例
const request = axios.create({
  baseURL: '/api', // 根据实际后端API地址配置
  timeout: 10000 // 请求超时时间
})

// 请求拦截器
request.interceptors.request.use(
  config => {
    // 在这里可以添加token等请求头
    // config.headers['Authorization'] = 'Bearer ' + getToken()
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  response => {
    // 对响应数据做处理
    return response.data
  },
  error => {
    // 对响应错误做处理
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

export default request